import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    roles: ['admin','editor']    // 设置该路由进入的权限，支持多个权限叠加
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path*",
        component: () => import("@/views/redirect/redirect"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login/login"),
    hidden: true,
  },

  {
    path: "/404",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "",
    component: Layout,
    redirect: "index",
    children: [
      {
        path: "index",
        component: () => import("@/views/home/<USER>"),
        name: "首页",
        meta: {
          title: "首页",
          icon: "dashboard",
          noCache: true,
          affix: true,
        },
      },
    ],
  },
  {
    path: "",
    component: Layout,
    redirect: "index",
    children: [
      {
        path: "orderDetail",
        component: () => import("@/views/projectManage/orderDetail.vue"),
        name: "工单详情",
        meta: {
          title: "工单详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "orderDeal",
        component: () => import("@/views/projectManage/orderDeal.vue"),
        name: "工单处理",
        meta: {
          title: "工单处理",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "orderList",
        component: () => import("@/views/projectManage/orderList.vue"),
        name: "工单列表",
        meta: {
          title: "工单列表",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "projectLogList",
        component: () => import("@/views/projectManage/components/log.vue"),
        name: "项目操作日志",
        meta: {
          title: "项目操作日志",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "operationWorkOrderAdd",
        component: () =>
          import("@/views/operationWorkOrder/components/add.vue"),
        name: "创建运维工单",
        meta: {
          title: "创建运维工单",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/operationWorkOrder/add",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/operationWorkOrder/components/add.vue"
          ),
        name: "operationWorkOrderNewAdd",
        meta: {
          title: "创建运维工单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/operationWorkOrder/edit",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/operationWorkOrder/components/edit.vue"
          ),
        name: "operationWorkOrderNewEdit",
        meta: {
          title: "编辑运维工单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "order/operationWorkOrder/list",
        component: () => import("@/views/operationWorkOrder/index.vue"),
        name: "operationWorkOrder",
        meta: {
          title: "运维工单",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/operationWorkOrder/list",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/operationWorkOrder/index.vue"
          ),
        name: "operationWorkOrderNew",
        meta: {
          title: "运维工单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/operationWorkOrder/handle",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/operationWorkOrder/components/handle.vue"
          ),
        name: "operationWorkOrderHandle",
        meta: {
          title: "处理工单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "operationWorkOrder/detail",
        component: () =>
          import("@/views/operationWorkOrder/components/detail.vue"),
        name: "运维工单详情",
        meta: {
          title: "运维工单详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/detail",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/operationWorkOrder/components/detail.vue"
          ),
        name: "operationWorkOrderDetail",
        meta: {
          title: "运维工单详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "stationRelation",
        component: () => import("@/views/stationRelation/index.vue"),
        name: "站点责任人管理",
        meta: {
          title: "站点责任人管理",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/workOrderType",
        component: () => import("@/views/workOrderType/index.vue"),
        name: "工单类型",
        meta: {
          title: "工单类型",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "faultType",
        component: () => import("@/views/faultType/index.vue"),
        name: "故障类别",
        meta: {
          title: "故障类别",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "om/task/detail",
        component: () => import("@/views/om/task/components/detail.vue"),
        name: "巡检详情",
        meta: {
          title: "巡检详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "om/task/index",
        component: () => import("@/views/om/task/index.vue"),
        name: "omTask",
        meta: {
          title: "巡检任务",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "om/station/list",
        component: () => import("@/views/om/station/index.vue"),
        name: "站点巡检管理",
        meta: {
          title: "站点巡检管理",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "om/rule/list",
        component: () => import("@/views/om/rule/index.vue"),
        name: "巡检规则",
        meta: {
          title: "巡检规则",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/errorPush/blackListStation",
        component: () => import("@/views/errorPush/blackListStation/index.vue"),
        name: "黑名单站点",
        meta: {
          title: "黑名单站点",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/errorPush/blackListStation",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/errorPush/blackListStation/index.vue"
          ),
        name: "maintenanceBlackListStation",
        meta: {
          title: "黑名单站点",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/errorPush/blackList",
        component: () => import("@/views/errorPush/blackList/index.vue"),
        name: "黑名单用户",
        meta: {
          title: "黑名单用户",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/errorPush/blackList",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/errorPush/blackList/index.vue"
          ),
        name: "maintenanceBlackList",
        meta: {
          title: "黑名单用户",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/errorPush/setProcessGroup",
        component: () => import("@/views/errorPush/setProcessGroup/index.vue"),
        name: "设置分组",
        meta: {
          title: "设置分组",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/station/record",
        component: () => import("@/views/station/record.vue"),
        name: "能投大区数据核验记录",
        meta: {
          title: "能投大区数据核验记录",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/station/level",
        component: () => import("@/views/station/level.vue"),
        name: "站点运维等级",
        meta: {
          title: "站点运维等级",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/chargingStation/gun",
        component: () => import("@/views/chargingStation/gun.vue"),
        name: "chargingGun",
        meta: {
          title: "充电枪",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/infoQuery/deviceList",
        component: () =>
          import("@/views/operationMaintenanceManage/deviceList/index.vue"),
        name: "maintenanceChargingGun",
        meta: {
          title: "设备列表",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/stationManage/chargingStation",
        component: () => import("@/views/chargingStation/index.vue"),
        name: "chargingStation",
        meta: {
          title: "充电桩",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/chargingStation/components/stationDetail",
        component: () =>
          import("@/views/chargingStation/components/stationDetail.vue"),
        name: "充电桩详情",
        meta: {
          title: "充电桩详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/chargingStation/components/gunDetail",
        component: () =>
          import("@/views/chargingStation/components/gunDetail.vue"),
        name: "充电枪详情",
        meta: {
          title: "充电枪详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/deviceList/gunDetail",
        component: () =>
          import("@/views/operationMaintenanceManage/deviceList/gunDetail.vue"),
        name: "maintenanceGunDetailList",
        meta: {
          title: "设备详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/infoQuery/equipmentBrandWarranty",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/equipmentBrandWarranty/index.vue"
          ),
        name: "设备品牌质保",
        meta: {
          title: "设备品牌质保",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/chargingOrder/detail",
        component: () => import("@/views/chargingOrder/detail.vue"),
        name: "充电订单详情",
        meta: {
          title: "充电订单详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/stationManage/chargingOrder",
        component: () => import("@/views/chargingOrder/index.vue"),
        name: "chargingOrder",
        meta: {
          title: "充电订单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/infoQuery/chargingOrder",
        component: () =>
          import("@/views/operationMaintenanceManage/chargingOrder/index.vue"),
        name: "chargingOrder2",
        meta: {
          title: "订单信息",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/chargingOrder/detail",
        component: () =>
          import("@/views/operationMaintenanceManage/chargingOrder/detail.vue"),
        name: "订单详情",
        meta: {
          title: "订单详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/station/newDetailPage",
        component: () => import("@/views/station/newDetailPage.vue"),
        name: "stationDetailPage",
        meta: {
          title: "站点详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/station/newDetailPage",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/station/newDetailPage.vue"
          ),
        name: "stationDetailPage2",
        meta: {
          title: "站点详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/errorPush/orderRules",
        component: () => import("@/views/errorPush/orderRules/index.vue"),
        name: "建单规则",
        meta: {
          title: "建单规则",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/errorPush/orderRules",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/errorPush/orderRules/index.vue"
          ),
        name: "maintenanceOrderRules",
        meta: {
          title: "建单规则",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/order/errorPush",
        component: () => import("@/views/errorPush/index.vue"),
        name: "errorPush",
        meta: {
          title: "异常上报信息",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/infoQuery/errorPush",
        component: () =>
          import("@/views/operationMaintenanceManage/errorPush/index.vue"),
        name: "maintenanceErrorPush",
        meta: {
          title: "异常信息",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/workOrderWorkBench/projectList",
        component: () => import("@/views/projectManage/index.vue"),
        name: "projectManage",
        meta: {
          title: "项目管理",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/workOrderWorkBench/workOrderWorkbench",
        component: () => import("@/views/workOrderWorkbench/index.vue"),
        name: "workOrderWorkbench",
        meta: {
          title: "工单工作台",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/demandPool/businessType",
        component: () =>
          import("@/views/demandPoolManage/demandPool/businessType.vue"),
        name: "demandPoolBusinessType",
        meta: {
          title: "业务类型",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/demandPool/demandType",
        component: () =>
          import("@/views/demandPoolManage/demandPool/demandType.vue"),
        name: "demandPoolDemandType",
        meta: {
          title: "需求类型",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/demandPool/setProcessGroup",
        component: () =>
          import(
            "@/views/demandPoolManage/demandPool/setProcessGroup/index.vue"
          ),
        name: "demandPoolSetGroup",
        meta: {
          title: "分组管理",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/demandPool/setDataPermission",
        component: () =>
          import(
            "@/views/demandPoolManage/demandPool/setDataPermission/index.vue"
          ),
        name: "demandPoolSetPermission",
        meta: {
          title: "账号数据权限",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/demandPool/formDesign",
        component: () =>
          import("@/views/demandPoolManage/formCreateList/formDesign.vue"),
        name: "demandPoolFormDesign",
        meta: {
          title: "表单设计",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/sortingRules/addRules",
        component: () => import("@/views/sortingRules/addRules.vue"),
        name: "addSortingRules",
        meta: {
          title: "配置清分",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationManage/sortingRules",
        component: () => import("@/views/sortingRules/index.vue"),
        name: "sortingRules",
        meta: {
          title: "站点清分规则",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/demandPoolManage/partnerManage/new",
        component: () =>
          import("@/views/demandPoolManage/partnerManage/components/new.vue"),
        name: "partnerManageAdd",
        meta: {
          title: "合作商新增",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/demandPoolManage/partnerManage/edit",
        component: () =>
          import("@/views/demandPoolManage/partnerManage/components/new.vue"),
        name: "partnerManageEdit",
        meta: {
          title: "合作商修改",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/demandPoolManage/partnerManage/detail",
        component: () =>
          import(
            "@/views/demandPoolManage/partnerManage/components/detail.vue"
          ),
        name: "partnerManageDetail",
        meta: {
          title: "合作商详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/demandPoolManage/demandPool",
        component: () =>
          import("@/views/demandPoolManage/demandPool/index.vue"),
        name: "demandPool",
        meta: {
          title: "需求池",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/demandPoolManage/demandPool/addDemand",
        component: () =>
          import(
            "@/views/demandPoolManage/demandPool/components/addDemand.vue"
          ),
        name: "demandAdd",
        meta: {
          title: "新增需求",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/demandPoolManage/demandPool/editDemand",
        component: () =>
          import(
            "@/views/demandPoolManage/demandPool/components/editDemand.vue"
          ),
        name: "demandEdit",
        meta: {
          title: "修改需求",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/demandPoolManage/demandPool/detail",
        component: () =>
          import("@/views/demandPoolManage/demandPool/components/detail.vue"),
        name: "demandDetail",
        meta: {
          title: "需求详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/checkGroup",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/checkGroup/index.vue"
          ),
        name: "checkGroupManage",
        meta: {
          title: "检查组",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/configManage/checkGroup/edit",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/checkGroup/add.vue"
          ),
        name: "checkGroupEdit",
        meta: {
          title: "编辑检查组",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/checkItems",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/checkItems/index.vue"
          ),
        name: "checkItemsManage",
        meta: {
          title: "检查项",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/checkItems/formDesign",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/checkItems/formDesign.vue"
          ),
        name: "checkItemsFormDesign",
        meta: {
          title: "表单设计",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/planConfig",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/planConfig/index.vue"
          ),
        name: "planConfigManage",
        meta: {
          title: "计划配置",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/configManage/planConfig/add",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/planConfig/add.vue"
          ),
        name: "planConfigAdd",
        meta: {
          title: "新增计划配置",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/configManage/planConfig/edit",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/planConfig/add.vue"
          ),
        name: "planConfigEdit",
        meta: {
          title: "编辑计划配置",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/configManage/planConfig/stationConfig",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/planConfig/components/stationConfig.vue"
          ),
        name: "planConfigStationEdit",
        meta: {
          title: "配置站点",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/accountPermission",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/accountPermission/index.vue"
          ),
        name: "accountPermissionManage",
        meta: {
          title: "账号权限",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/accountPermission/edit",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/accountPermission/edit.vue"
          ),
        name: "accountPermissionEdit",
        meta: {
          title: "编辑账号权限",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/timeConfig/config",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/timeConfig/components/config.vue"
          ),
        name: "timeConfigStation",
        meta: {
          title: "配置站点",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/configManage/timeConfig",
        component: () =>
          import(
            "@/views/operationMaintenanceManage/configManage/timeConfig/index.vue"
          ),
        name: "timeConfig",
        meta: {
          title: "时效配置",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/engineerProject/investBatchList",
        component: () =>
          import("@/views/engineerProject/investBatchList/index.vue"),
        name: "investBatchList",
        meta: {
          title: "项目投建批次",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/engineerProject/timelinessEquation",
        component: () =>
          import("@/views/engineerProject/timelinessEquation/index.vue"),
        name: "timelinessEquation",
        meta: {
          title: "项目时效统计",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/engineerProject/setting/accountAuthority",
        component: () =>
          import("@/views/engineerProject/setting/accountAuthority/index.vue"),
        name: "accountAuthority",
        meta: {
          title: "账号权限",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/engineerProject/setting/timeConfig",
        component: () =>
          import("@/views/engineerProject/setting/timeConfig/index.vue"),
        name: "engineerTimeConfig",
        meta: {
          title: "时效配置",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/engineerProject/investBatchList/apply",
        component: () =>
          import("@/views/engineerProject/investBatchList/apply.vue"),
        name: "investBatchApply",
        meta: {
          title: "开工申请",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/engineerProject/investBatchList/completeAcceptance",
        component: () =>
          import(
            "@/views/engineerProject/investBatchList/completeAcceptance.vue"
          ),
        name: "completeAcceptance",
        meta: {
          title: "竣工验收",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/engineerProject/investBatchList/constructAcceptance",
        component: () =>
          import(
            "@/views/engineerProject/investBatchList/constructAcceptance.vue"
          ),
        name: "constructAcceptance",
        meta: {
          title: "施工验收",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/engineerProject/investBatchList/detail",
        component: () =>
          import("@/views/engineerProject/investBatchList/detail.vue"),
        name: "investBatchDetail",
        meta: {
          title: "项目详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/engineerProject/investBatchList/notice",
        component: () =>
          import("@/views/engineerProject/investBatchList/notice.vue"),
        name: "rectificationNotice",
        meta: {
          title: "整改通知单",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/engineerProject/investBatchList/operateConfirm",
        component: () =>
          import("@/views/engineerProject/investBatchList/operateConfirm.vue"),
        name: "operateConfirm",
        meta: {
          title: "项目投运确认单",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/engineerProject/station/detail",
        component: () => import("@/views/engineerProject/station/detail.vue"),
        name: "epStationDetail",
        meta: {
          title: "场站详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/engineerProject/station",
        component: () => import("@/views/engineerProject/station/index.vue"),
        name: "engineerStationPage",
        meta: {
          title: "场站项目列表",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/engineerProject/projectCost",
        component: () =>
          import("@/views/engineerProject/projectCost/index.vue"),
        name: "projectCost",
        meta: {
          title: "项目成本",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationMaintenanceManage/infoQuery/station",
        component: () =>
          import("@/views/operationMaintenanceManage/station/index.vue"),
        name: "maintenanceStationList",
        meta: {
          title: "站点信息",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/stationManage/device",
        component: () => import("@/views/device/index.vue"),
        name: "deviceList",
        meta: {
          title: "设备信息",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/stationManage/chargingPileManage",
        component: () => import("@/views/chargingPile/index.vue"),
        name: "pileList",
        meta: {
          title: "充电桩信息",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationManage/constructionTeam",
        component: () =>
          import("@/views/operationManage/constructionTeam/index.vue"),
        name: "constructionTeam",
        meta: {
          title: "施工队",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/operationManage/provider",
        component: () => import("@/views/provider/index.vue"),
        name: "providerManage",
        meta: {
          title: "服务商管理",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/ledger/configManage/formCreateList/formDesign",
        component: () =>
          import("@/views/ledger/configManage/formCreateList/formDesign.vue"),
        name: "ledgerFormDesign",
        meta: {
          title: "表单设计",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/configManage/formCreateList",
        component: () =>
          import("@/views/ledger/configManage/formCreateList/index.vue"),
        name: "ledgerForm",
        meta: {
          title: "表单设计",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/configManage/process",
        component: () =>
          import("@/views/ledger/configManage/process/LFList.vue"),
        name: "ledgerProcessList",
        meta: {
          title: "流程管理",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/configManage/setDataPermission",
        component: () =>
          import("@/views/ledger/configManage/setDataPermission/index.vue"),
        name: "ledgerPermission",
        meta: {
          title: "账号权限",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/configManage/setGroup",
        component: () =>
          import("@/views/ledger/configManage/setGroup/index.vue"),
        name: "ledgerGroupManage",
        meta: {
          title: "分组管理",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/configManage/company",
        component: () =>
          import("@/views/ledger/configManage/company/index.vue"),
        name: "ledgerCompany",
        meta: {
          title: "公司机构",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/company/new",
        component: () =>
          import("@/views/ledger/configManage/company/components/new.vue"),
        name: "companyManageAdd",
        meta: {
          title: "新增公司",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/company/edit",
        component: () =>
          import("@/views/ledger/configManage/company/components/new.vue"),
        name: "companyManageEdit",
        meta: {
          title: "修改公司",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/company/detail",
        component: () =>
          import("@/views/ledger/configManage/company/components/detail.vue"),
        name: "companyManageDetail",
        meta: {
          title: "公司详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/configManage/process/design",
        component: () =>
          import("@/views/ledger/configManage/process/index.vue"),
        name: "ledgerProcess",
        meta: {
          title: "流程图制作",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/ledgerManage",
        component: () => import("@/views/ledger/ledgerManage/index.vue"),
        name: "ledgerList",
        meta: {
          title: "工单台账管理",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/ledgerManage/detail",
        component: () => import("@/views/ledger/ledgerManage/detail.vue"),
        name: "ledgerDetail",
        meta: {
          title: "工单详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/ledgerManage/handle",
        component: () => import("@/views/ledger/ledgerManage/handle.vue"),
        name: "ledgerHandle",
        meta: {
          title: "处理工单",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/ledgerManage/add",
        component: () => import("@/views/ledger/ledgerManage/add.vue"),
        name: "ledgerAddPage",
        meta: {
          title: "新增工单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/ledger/ledgerManage/edit",
        component: () => import("@/views/ledger/ledgerManage/add.vue"),
        name: "ledgerAddPage",
        meta: {
          title: "修改工单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/ledger/ledgerManage/addOrder",
        component: () => import("@/views/ledger/ledgerManage/add.vue"),
        name: "ledgerAddOrder",
        meta: {
          title: "加单",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/addNewRecord",
        component: () => import("@/views/ledger/addRecord/newAddRecord.vue"),
        name: "ledgerNewAddRecord",
        meta: {
          title: "加单记录",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/addRecord",
        component: () => import("@/views/ledger/addRecord/index.vue"),
        name: "ledgerAddRecord",
        meta: {
          title: "历史加单记录",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/dashboard",
        component: () => import("@/views/ledger/dashboard/index.vue"),
        name: "ledgerDashboard",
        meta: {
          title: "数据统计",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/workHours",
        component: () => import("@/views/ledger/workHours/index.vue"),
        name: "ledgerWorkHours",
        meta: {
          title: "工时管理",
          icon: "dashboard",
          noCache: false,
          affix: false,
          // alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/engineerProject/backlog",
        component: () => import("@/views/engineerProject/backlog/index.vue"),
        name: "backlogList",
        meta: {
          title: "待办",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/ledger/configManage/urgencyDegree",
        component: () =>
          import("@/views/ledger/configManage/urgencyDegree/index.vue"),
        name: "urgencyConfigPage",
        meta: {
          title: "紧急程度",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/ledger/configManage/department",
        component: () =>
          import("@/views/ledger/configManage/department/index.vue"),
        name: "departmentPage",
        meta: {
          title: "部门管理",
          icon: "dashboard",
          noCache: false,
          affix: false,
          alwaysRemainCache: true, //自定义字段：关闭标签页还保留缓存
        },
      },
      {
        path: "/archive",
        component: () => import("@/views/archive/index.vue"),
        name: "archivePage",
        meta: {
          title: "人员档案",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/archive/detail",
        component: () => import("@/views/archive/detail.vue"),
        name: "archiveDetailPage",
        meta: {
          title: "人员详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/archive/add",
        component: () => import("@/views/archive/add.vue"),
        name: "archiveAddPage",
        meta: {
          title: "新增人员",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/archive/edit",
        component: () => import("@/views/archive/add.vue"),
        name: "archiveAddPage",
        meta: {
          title: "编辑人员",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/infoArchive/contractAgreement/agreement/list/detail",
        component: () =>
          import(
            "@/views/infoArchive/contractAgreement/agreement/list/detail.vue"
          ),
        name: "agreementDetail",
        meta: {
          title: "协议合同详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/stationManage/contractAgreement/agreement/list",
        component: () =>
          import(
            "@/views/infoArchive/contractAgreement/agreement/list/index.vue"
          ),
        name: "agreementList",
        meta: {
          title: "合同协议",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/stationManage/contractAgreement/agreement/notice",
        component: () =>
          import(
            "@/views/infoArchive/contractAgreement/agreement/notice/index.vue"
          ),
        name: "contractNoticePage",
        meta: {
          title: "预警管理",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/stationManage/partner",
        component: () => import("@/views/infoArchive/partner/index.vue"),
        name: "agreementPartnerList",
        meta: {
          title: "合作商档案",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/ledger/commonEmail",
        component: () => import("@/views/ledger/commonEmail/index.vue"),
        name: "commonMailList",
        meta: {
          title: "公共邮箱",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/ledger/commonEmail/detail",
        component: () => import("@/views/ledger/commonEmail/detail.vue"),
        name: "邮件详情",
        meta: {
          title: "邮件详情",
          icon: "dashboard",
          noCache: true,
          affix: false,
        },
      },
      {
        path: "/ledger/commonEmail/config",
        component: () => import("@/views/ledger/commonEmail/config.vue"),
        name: "commonEmailConfig",
        meta: {
          title: "邮件配置",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/ledger/commonEmail/recheck",
        component: () => import("@/views/ledger/commonEmail/recheck.vue"),
        name: "commonEmailRecheck",
        meta: {
          title: "邮件复核",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/lifePay/alipayAndOrg",
        component: () =>
          import("@/views/settlement/lifePay/alipayAndOrg/index.vue"),
        name: "alipayAndOrg",
        meta: {
          title: "支付宝与机构账单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/lifePay/alipayAndBangdao",
        component: () =>
          import("@/views/settlement/lifePay/alipayAndBangdao/index.vue"),
        name: "alipayAndBangdao",
        meta: {
          title: "支付宝与邦道账单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/lifePay/publicService",
        component: () =>
          import("@/views/settlement/lifePay/publicService/index.vue"),
        name: "publicService",
        meta: {
          title: "公服相关数据",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/lifePay/organization",
        component: () =>
          import("@/views/settlement/lifePay/organization/index.vue"),
        name: "lifePayOrganization",
        meta: {
          title: "机构信息",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/lifePay/serviceProvider",
        component: () =>
          import("@/views/settlement/lifePay/serviceProvider/index.vue"),
        name: "lifePayServiceProvider",
        meta: {
          title: "服务商信息",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/lifePay/bangdaoAndService",
        component: () =>
          import("@/views/settlement/lifePay/bangdaoAndService/index.vue"),
        name: "bangdaoAndService",
        meta: {
          title: "邦道与服务商账单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/xdt/charge",
        component: () => import("@/views/settlement/xdt/charge/index.vue"),
        name: "xdtCharge",
        meta: {
          title: "新电途充电",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/xdt/storageEnergy",
        component: () =>
          import("@/views/settlement/xdt/storageEnergy/index.vue"),
        name: "xdtStorageEnergy",
        meta: {
          title: "新电途储能",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/destinationCharge/settleAnalysis",
        component: () =>
          import(
            "@/views/settlement/destinationCharge/settleAnalysis/index.vue"
          ),
        name: "destinationSettleAnalysis",
        meta: {
          title: "场站结算情况分析",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/destinationCharge/serviceBill",
        component: () =>
          import("@/views/settlement/destinationCharge/serviceBill/index.vue"),
        name: "destinationServiceBill",
        meta: {
          title: "服务商账单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/destinationCharge/electricityBill",
        component: () =>
          import(
            "@/views/settlement/destinationCharge/electricityBill/index.vue"
          ),
        name: "destinationElectricityBill",
        meta: {
          title: "电费账单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/destinationCharge/partnerBill",
        component: () =>
          import("@/views/settlement/destinationCharge/partnerBill/index.vue"),
        name: "destinationPartnerBill",
        meta: {
          title: "合伙人账单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/destinationCharge/middlemanBill",
        component: () =>
          import(
            "@/views/settlement/destinationCharge/middlemanBill/index.vue"
          ),
        name: "destinationMiddlemanBill",
        meta: {
          title: "居间商账单",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/destinationCharge/accountNo",
        component: () =>
          import("@/views/settlement/destinationCharge/accountNo/index.vue"),
        name: "destinationAccountNo",
        meta: {
          title: "户号",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
      {
        path: "/settlement/archive/index",
        component: () => import("@/views/settlement/archive/index.vue"),
        name: "settlementArchive",
        meta: {
          title: "结算材料归档",
          icon: "dashboard",
          noCache: false,
          affix: false,
        },
      },
    ],
  },
  {
    hidden: false,
    path: "/demo",
    component: Layout,
    children: [
      {
        path: "demo",
        component: () => import("@/views/demo/index.vue"),
        name: "demo",
        meta: { title: "demo", icon: "dashboard", noCache: true },
      },
      {
        path: "buse",
        component: () => import("@/views/demo/buse/index.vue"),
        name: "buse",
        meta: { title: "buse", icon: "dashboard", noCache: true },
      },
      {
        path: "batch",
        component: () => import("@/views/demo/buse/batchSelect.vue"),
        name: "buseBatch",
        meta: { title: "buseDemo2", icon: "dashboard", noCache: true },
      },
      {
        path: "spreadsheet",
        component: () => import("@/views/demo/spreadsheet/index.vue"),
        name: "spreadsheet",
        meta: { title: "在线表格", icon: "table", noCache: true },
      },
    ],
  },
  {
    path: "/dict",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "type/data/:dictId(\\d+)",
        component: (resolve) => require(["@/views/system/dict/data"], resolve),
        name: "Data",
        meta: { title: "字典数据", icon: "" },
      },
    ],
  },
  // 不在菜单栏的静态路由
  {
    path: "/process", //工作流
    component: Layout,
    redirect: "noredirect",
    meta: { icon: "system", title: "" },
    hidden: true,
    children: [
      {
        path: "process",
        component: () => import("@/views/process/index.vue"),
        name: "流程图制作",
        meta: { title: "流程图制作", icon: "tree-table" },
      },
      {
        path: "formDesign",
        component: () => import("@/views/process/formDesign.vue"),
        name: "formDesign",
        meta: { title: "表单设计", icon: "drag" },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "个人中心",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
];
export default new Router({
  mode: "history", //
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
  base: "/charging-maintenance-ui",
});
